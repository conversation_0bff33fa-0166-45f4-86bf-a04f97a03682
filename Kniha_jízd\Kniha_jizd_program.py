"""
Kni<PERSON> jízd <PERSON> - automatická kontrola odevzdání knih jízd
<PERSON> mě<PERSON> kontroluje, kdo odevzdal knihu jízd v termínu (25. př<PERSON><PERSON><PERSON><PERSON><PERSON> m<PERSON> - 5. akt<PERSON><PERSON><PERSON><PERSON><PERSON>)
"""

import time
import datetime
from datetime import datetime, timedelta
import re
import pandas as pd
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

# Konfigurace
TAB_URL = "https://intranet.img-management.cz/Porady/KNIHY%20JZD/Forms/AllItems.aspx?&&p_SortBehavior=0&p_FileLeafRef=9AL%206014%20%2d%202025%2exls&&PageFirstRow=1&&View={DD08046D-A508-4D0B-BA78-829B558AEBC1}"
USERNAME = "januskova"
PASSWORD = "janu943"

# Nastavení kontroly
DAYS_BEFORE = 5  # 5 dní před koncem měsíce
DAYS_AFTER = 5   # 5 dní po začátku měsíce
SPZ_LENGTH = 7   # Délka SPZ

class KnihaJizdBot:
    def __init__(self):
        self.driver = None
        self.excel_data = None

    def setup_driver(self):
        """Nastavení Chrome driveru"""
        chrome_options = Options()
        chrome_options.add_argument("--headless")  # Spustit bez GUI
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")

        service = Service(ChromeDriverManager().install())
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
        self.driver.implicitly_wait(10)

    def login_to_intranet(self):
        """Přihlášení k intranetu"""
        try:
            print("Přihlašuji se k intranetu...")
            self.driver.get(TAB_URL)

            # Čekání na načtení přihlašovací stránky
            username_field = WebDriverWait(self.driver, 20).until(
                EC.presence_of_element_located((By.NAME, "username"))
            )

            # Vyplnění přihlašovacích údajů
            username_field.send_keys(USERNAME)
            password_field = self.driver.find_element(By.NAME, "password")
            password_field.send_keys(PASSWORD)

            # Kliknutí na přihlášení
            login_button = self.driver.find_element(By.XPATH, "//input[@type='submit']")
            login_button.click()

            # Čekání na přesměrování
            WebDriverWait(self.driver, 20).until(
                EC.url_contains("intranet.img-management.cz")
            )

            print("Úspěšně přihlášen!")
            return True

        except Exception as e:
            print(f"Chyba při přihlašování: {e}")
            return False

    def get_files_from_intranet(self):
        """Získání seznamu souborů z intranetu s daty změny"""
        try:
            print("Získávám seznam souborů z intranetu...")

            # Navigace na stránku s knihami jízd
            self.driver.get(TAB_URL)
            time.sleep(3)

            files_data = []

            # Hledání tabulky se soubory
            table = WebDriverWait(self.driver, 20).until(
                EC.presence_of_element_located((By.CLASS_NAME, "ms-listviewtable"))
            )

            rows = table.find_elements(By.TAG_NAME, "tr")

            for row in rows[1:]:  # Přeskočit hlavičku
                cells = row.find_elements(By.TAG_NAME, "td")
                if len(cells) >= 3:
                    # Název souboru
                    file_name = cells[0].text.strip()

                    # Datum změny
                    modified_date = cells[2].text.strip()

                    if file_name and modified_date:
                        files_data.append({
                            'filename': file_name,
                            'modified_date': modified_date
                        })

            print(f"Nalezeno {len(files_data)} souborů")
            return files_data

        except Exception as e:
            print(f"Chyba při získávání souborů: {e}")
            return []

    def load_excel_data(self, excel_path):
        """Načtení Excel tabulky s SPZ a řidiči"""
        try:
            print(f"Načítám Excel soubor: {excel_path}")
            self.excel_data = pd.read_excel(excel_path)

            # Předpokládáme sloupce: SPZ, Jmeno, Email
            required_columns = ['SPZ', 'Jmeno', 'Email']

            if not all(col in self.excel_data.columns for col in required_columns):
                print(f"Excel soubor musí obsahovat sloupce: {required_columns}")
                return False

            print(f"Načteno {len(self.excel_data)} záznamů řidičů")
            return True

        except Exception as e:
            print(f"Chyba při načítání Excel souboru: {e}")
            return False

    def extract_spz_from_filename(self, filename):
        """Extrakce SPZ ze jména souboru"""
        # Hledání 7-znakové SPZ v názvu souboru
        spz_pattern = r'[A-Z0-9]{7}'
        matches = re.findall(spz_pattern, filename.upper())

        for match in matches:
            if len(match) == SPZ_LENGTH:
                return match

        return None

    def get_control_period(self, target_month=None, target_year=None):
        """Získání období pro kontrolu (25. předchozího měsíce - 5. aktuálního měsíce)"""
        if target_month is None or target_year is None:
            now = datetime.now()
            target_month = now.month
            target_year = now.year

        # Začátek období - 25. předchozího měsíce
        if target_month == 1:
            start_month = 12
            start_year = target_year - 1
        else:
            start_month = target_month - 1
            start_year = target_year

        start_date = datetime(start_year, start_month, 25)

        # Konec období - 5. aktuálního měsíce
        end_date = datetime(target_year, target_month, 5)

        return start_date, end_date

    def parse_date(self, date_string):
        """Parsování data z různých formátů"""
        date_formats = [
            '%d.%m.%Y %H:%M',
            '%d.%m.%Y',
            '%Y-%m-%d %H:%M:%S',
            '%Y-%m-%d'
        ]

        for fmt in date_formats:
            try:
                return datetime.strptime(date_string, fmt)
            except ValueError:
                continue

        print(f"Nepodařilo se parsovat datum: {date_string}")
        return None

    def check_submission_deadline(self, files_data, target_month=None, target_year=None):
        """Kontrola termínu odevzdání knih jízd"""
        start_date, end_date = self.get_control_period(target_month, target_year)

        print(f"Kontroluji období: {start_date.strftime('%d.%m.%Y')} - {end_date.strftime('%d.%m.%Y')}")

        submitted_drivers = []
        missing_drivers = []

        # Projít všechny soubory a najít ty, které byly odevzdány v termínu
        for file_info in files_data:
            filename = file_info['filename']
            modified_date_str = file_info['modified_date']

            # Extrakce SPZ ze jména souboru
            spz = self.extract_spz_from_filename(filename)
            if not spz:
                continue

            # Parsování data změny
            modified_date = self.parse_date(modified_date_str)
            if not modified_date:
                continue

            # Kontrola, zda je datum v požadovaném období
            if start_date <= modified_date <= end_date:
                # Najít řidiče podle SPZ
                driver_info = self.find_driver_by_spz(spz)
                if driver_info:
                    submitted_drivers.append({
                        'spz': spz,
                        'driver': driver_info,
                        'submitted_date': modified_date,
                        'filename': filename
                    })

        # Najít řidiče, kteří neodevzdali
        all_drivers = self.excel_data.to_dict('records') if self.excel_data is not None else []

        for driver in all_drivers:
            spz = driver['SPZ']
            if not any(sub['spz'] == spz for sub in submitted_drivers):
                missing_drivers.append(driver)

        return submitted_drivers, missing_drivers

    def find_driver_by_spz(self, spz):
        """Najít řidiče podle SPZ"""
        if self.excel_data is None:
            return None

        matching_drivers = self.excel_data[self.excel_data['SPZ'] == spz]

        if not matching_drivers.empty:
            return matching_drivers.iloc[0].to_dict()

        return None

    def send_email(self, to_email, driver_name, spz):
        """Odeslání upomínky emailem"""
        try:
            # Nastavení SMTP (upravte podle vašeho poskytovatele)
            smtp_server = "smtp.gmail.com"
            smtp_port = 587
            from_email = "<EMAIL>"  # Změňte na váš email
            from_password = "your_app_password"   # Změňte na vaše heslo aplikace

            # Vytvoření zprávy
            msg = MIMEMultipart()
            msg['From'] = from_email
            msg['To'] = to_email
            msg['Subject'] = "Upomínka - Kniha jízd neodevzdána"

            body = f"""
Dobrý den {driver_name},

upozorňujeme Vás, že jste neodevzdal/a knihu jízd pro vozidlo SPZ: {spz} v požadovaném termínu.

Prosím odevzdejte knihu jízd co nejdříve.

S pozdravem,
Systém kontroly knih jízd
"""

            msg.attach(MIMEText(body, 'plain', 'utf-8'))

            # Odeslání emailu
            server = smtplib.SMTP(smtp_server, smtp_port)
            server.starttls()
            server.login(from_email, from_password)
            text = msg.as_string()
            server.sendmail(from_email, to_email, text)
            server.quit()

            print(f"Email odeslán na: {to_email}")
            return True

        except Exception as e:
            print(f"Chyba při odesílání emailu na {to_email}: {e}")
            return False

    def update_excel_with_status(self, missing_drivers, excel_path):
        """Aktualizace Excel tabulky se stavem odevzdání"""
        try:
            # Přidání sloupce se stavem, pokud neexistuje
            if 'Status' not in self.excel_data.columns:
                self.excel_data['Status'] = 'OK'

            # Označení řidičů, kteří neodevzdali
            for driver in missing_drivers:
                spz = driver['SPZ']
                self.excel_data.loc[self.excel_data['SPZ'] == spz, 'Status'] = 'NEODEVZDÁNO'

            # Uložení aktualizované tabulky
            self.excel_data.to_excel(excel_path, index=False)
            print(f"Excel tabulka aktualizována: {excel_path}")
            return True

        except Exception as e:
            print(f"Chyba při aktualizaci Excel tabulky: {e}")
            return False

    def run_monthly_check(self, excel_path, send_emails=False, update_excel=True):
        """Spuštění měsíční kontroly"""
        try:
            print("=== SPUŠTĚNÍ MĚSÍČNÍ KONTROLY KNIH JÍZD ===")

            # Nastavení driveru
            self.setup_driver()

            # Přihlášení k intranetu
            if not self.login_to_intranet():
                return False

            # Načtení Excel dat
            if not self.load_excel_data(excel_path):
                return False

            # Získání souborů z intranetu
            files_data = self.get_files_from_intranet()
            if not files_data:
                print("Nepodařilo se získat data ze souborů")
                return False

            # Kontrola termínu odevzdání
            submitted_drivers, missing_drivers = self.check_submission_deadline(files_data)

            # Výpis výsledků
            print(f"\n=== VÝSLEDKY KONTROLY ===")
            print(f"Odevzdali v termínu: {len(submitted_drivers)} řidičů")
            print(f"Neodevzdali: {len(missing_drivers)} řidičů")

            if submitted_drivers:
                print("\nŘidiči, kteří odevzdali:")
                for driver in submitted_drivers:
                    print(f"- {driver['driver']['Jmeno']} (SPZ: {driver['spz']}) - {driver['submitted_date'].strftime('%d.%m.%Y')}")

            if missing_drivers:
                print("\nŘidiči, kteří NEODEVZDALI:")
                for driver in missing_drivers:
                    print(f"- {driver['Jmeno']} (SPZ: {driver['SPZ']}) - Email: {driver.get('Email', 'N/A')}")

                # Odeslání emailů
                if send_emails:
                    print("\nOdesílám upomínky emailem...")
                    for driver in missing_drivers:
                        if driver.get('Email'):
                            self.send_email(driver['Email'], driver['Jmeno'], driver['SPZ'])

                # Aktualizace Excel tabulky
                if update_excel:
                    self.update_excel_with_status(missing_drivers, excel_path)

            return True

        except Exception as e:
            print(f"Chyba při spuštění kontroly: {e}")
            return False

        finally:
            if self.driver:
                self.driver.quit()

    def cleanup(self):
        """Ukončení a úklid"""
        if self.driver:
            self.driver.quit()


def main():
    """Hlavní funkce"""
    print("Kniha jízd Bot - automatická kontrola odevzdání")

    # Cesta k Excel souboru s řidiči a SPZ
    excel_file = input("Zadejte cestu k Excel souboru s řidiči a SPZ (nebo Enter pro 'ridici_spz.xlsx'): ").strip()
    if not excel_file:
        excel_file = "ridici_spz.xlsx"

    # Možnosti spuštění
    send_emails = input("Odeslat upomínky emailem? (y/n): ").lower() == 'y'
    update_excel = input("Aktualizovat Excel tabulku se stavem? (y/n): ").lower() == 'y'

    # Vytvoření instance bota
    bot = KnihaJizdBot()

    try:
        # Spuštění kontroly
        success = bot.run_monthly_check(excel_file, send_emails, update_excel)

        if success:
            print("\n=== KONTROLA DOKONČENA ÚSPĚŠNĚ ===")
        else:
            print("\n=== KONTROLA SELHALA ===")

    except KeyboardInterrupt:
        print("\nKontrola přerušena uživatelem")
    except Exception as e:
        print(f"Neočekávaná chyba: {e}")
    finally:
        bot.cleanup()


if __name__ == "__main__":
    main()