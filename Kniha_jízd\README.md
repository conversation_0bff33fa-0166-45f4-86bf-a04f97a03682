# Kniha jízd Bot

Automatická kontrola odevzdání knih jízd pro IMG Management.

## Funkce

- **Automatická kontrola**: <PERSON><PERSON><PERSON><PERSON> měs<PERSON>c kontroluje, kdo odevzdal knihu jízd v termínu
- **<PERSON><PERSON><PERSON> kontroly**: 25. p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> m<PERSON> až 5. aktu<PERSON>ln<PERSON><PERSON> m<PERSON>
- **Extrakce SPZ**: Automaticky extrahuje 7-znakové SPZ ze jmen souborů
- **Email upomínky**: Odesí<PERSON><PERSON> upomín<PERSON>, kte<PERSON><PERSON> neodev<PERSON>
- **Excel aktualizace**: Aktualizuje Excel tabulku se stavem odevzdání

## Požadavky

Nainstalujte potřebné knihovny:

```bash
pip install selenium pandas openpyxl webdriver-manager
```

## Příprava

1. **Excel soubor**: Vytvořte Excel soubor `ridici_spz.xlsx` se sloupci:
   - `SPZ` - SPZ vozidla (7 znaků)
   - `<PERSON><PERSON><PERSON>` - <PERSON><PERSON><PERSON>
   - `Email` - <PERSON><PERSON>

2. **<PERSON>ail nastavení**: V souboru upravte email nastavení v metodě `send_email()`:
   - `from_email` - váš email
   - `from_password` - heslo aplikace (pro Gmail)

## Spuštění

```bash
python Kniha_jizd_program.py
```

Program se zeptá na:
- Cestu k Excel souboru
- Zda odeslat upomínky emailem
- Zda aktualizovat Excel tabulku

## Jak to funguje

1. **Přihlášení**: Bot se přihlásí k intranetu IMG Management
2. **Stažení dat**: Získá seznam souborů a jejich data změny
3. **Kontrola termínu**: Zkontroluje, které soubory byly změněny v požadovaném období
4. **Extrakce SPZ**: Ze jmen souborů extrahuje SPZ vozidel
5. **Porovnání**: Porovná s Excel tabulkou řidičů
6. **Výsledky**: Vypíše, kdo odevzdal a kdo ne
7. **Akce**: Odešle upomínky a/nebo aktualizuje Excel

## Příklad Excel souboru

| SPZ     | Jmeno        | Email              |
|---------|--------------|-------------------|
| 9AL6014 | Jan Novák    | <EMAIL>  |
| 8BM5023 | Marie Svoboda| <EMAIL> |

## Poznámky

- SPZ musí mít přesně 7 znaků
- Program hledá SPZ v názvech souborů
- Kontrolní období: 25. předchozího měsíce - 5. aktuálního měsíce
- Pro Gmail použijte heslo aplikace, ne běžné heslo
